import { createBrowserRouter } from "react-router-dom";
import { ResponsiveLayout } from "./layouts/ResponsiveLayout";
import ProTableRefactored from "./components/ProTableRefactored";
import TestRefactored from "./components/TestRefactored";
import ReactVTableExample from "./components/VTable";
import TableWithIndeterminate from "./components/TableWithIndeterminate";
import VTableConfigComparison from "./components/VTableConfigComparison";
import VTableTest from "./components/VTableTest";
import VTableSimple from "./components/VTableSimple";

export const router = createBrowserRouter([
  {
    path: "/",
    Component: ResponsiveLayout,
    children: [
      {
        index: true,
        Component: () => <ProTableRefactored />,
      },
      {
        path: "protable",
        Component: () => <ProTableRefactored />,
      },
      {
        path: "test",
        Component: TestRefactored,
      },
    ],
  },
  {
    path: "*",
    Component: () => <div>404 Not Found</div>,
  },
  {
    path: "/vtable",
    Component: () => <ReactVTableExample />,
  },
  {
    path: "/table",
    Component: () => <TableWithIndeterminate />,
  },
  {
    path: "/vtable-config",
    Component: () => <VTableConfigComparison />,
  },
  {
    path: "/vtable-test",
    Component: () => <VTableTest />,
  },
  {
    path: "/vtable-simple",
    Component: () => <VTableSimple />,
  },
]);
